#include "FeatheringRenderer.h"
#include <QDebug>
#include <QtMath>

FeatheringRenderer::FeatheringRenderer()
{
}

void FeatheringRenderer::drawPathWithFeathering(QPainter* painter, const QPainterPath& path,
                                              const QPen& pen, const QBrush& brush, ToolType toolType)
{
    if (path.isEmpty() || !painter) {
        return;
    }

    // 保存原始状态
    painter->save();
    
    // 是否开启羽化
    if (toolType != ToolType::FreeDraw || !FEATHERING_ENABLED) {
        painter->setPen(pen);
        painter->setBrush(brush);
        // painter->drawPath(path);

        // 添加笔锋效果（只对自由绘制工具）
        if (isFreeDrawTool(toolType) && !path.isEmpty()) {
            drawBrushTip(painter, path, pen);
        }

        painter->restore();
        return;
    }

    // 获取羽化配置
    FeatheringConfig config = getFeatheringConfig(pen.widthF());

    // 从外到内绘制羽化层
    for (int i = config.layers; i >= 0; i--) {
        QPen featherPen = createFeatherPen(pen, i, config);

        // 绘制当前层
        painter->setPen(featherPen);
        painter->setBrush(brush);
        painter->drawPath(path);
    }
    
    painter->restore();
}

FeatheringRenderer::FeatheringConfig FeatheringRenderer::getFeatheringConfig(qreal lineWidth)
{
    // 根据FloatMenuConstants中的线条宽度返回预设配置
    // PEN_WIDTH_SMALL: 4.3 (适配后)
    // PEN_WIDTH_MEDIUM: 8.5 (适配后)
    // PEN_WIDTH_LARGE: 12.8 (适配后)

    if (lineWidth <= 6.5) {
        return SMALL_PEN_CONFIG;   // 小画笔配置 (≤6.5)
    } else if (lineWidth <= 10.5) {
        return MEDIUM_PEN_CONFIG;  // 中画笔配置 (6.5-10.5)
    } else {
        return LARGE_PEN_CONFIG;   // 大画笔配置 (>10.5)
    }
}

QPen FeatheringRenderer::createFeatherPen(const QPen& originalPen, int layerIndex,
                                         const FeatheringConfig& config)
{
    QPen featherPen = originalPen;
    QColor originalColor = originalPen.color();
    qreal originalWidth = originalPen.widthF();

    featherPen.setCapStyle(Qt::RoundCap);
    featherPen.setJoinStyle(Qt::RoundJoin);

    if (layerIndex == 0) {
        // 最内层：使用原始颜色和宽度
        featherPen.setColor(originalColor);
        featherPen.setWidthF(originalWidth);
    } else {
        // 羽化层：调整透明度和宽度
        qreal layerRatio = static_cast<qreal>(layerIndex) / config.layers;

        qreal alphaMultiplier = 0.3 + (1.0 - layerRatio) * 0.7 * config.intensity;
        qreal widthMultiplier = 1.0 + layerRatio * config.widthFactor;

        // 设置透明度
        QColor featherColor = originalColor;
        int newAlpha = static_cast<int>(originalColor.alpha() * alphaMultiplier);
        featherColor.setAlpha(qMax(30, qMin(255, newAlpha))); // 确保最小30的透明度，最大255

        // 设置线条宽度
        qreal newWidth = originalWidth * widthMultiplier;
        featherPen.setColor(featherColor);
        featherPen.setWidthF(newWidth);
    }

    return featherPen;
}

void FeatheringRenderer::drawBrushTip(QPainter* painter, const QPainterPath& path, const QPen& pen)
{
    if (path.isEmpty() || !painter) {
        return;
    }

    qreal penWidth = pen.widthF();

    // 保存原始状态
    painter->save();
    painter->setRenderHint(QPainter::Antialiasing, true);
    painter->setBrush(pen.brush());

    QPen variablePen = pen;

    // 绘制变宽度线条
    qreal tipLength = 0.15; // 前15%是笔锋部分
    qreal minWidth = penWidth * 0.1; // 最细宽度

    for (qreal t = 0; t < 1.0; t += 0.01) {
        qreal currentWidth;

        if (t <= tipLength) {
            // 前15%：从细到粗
            currentWidth = minWidth + (penWidth - minWidth) * (t / tipLength);
        } else {
            // 后85%：保持正常宽度
            currentWidth = penWidth;
        }

        variablePen.setWidthF(currentWidth);
        painter->setPen(variablePen);

        // 绘制当前点到下一点的小段
        if (t + 0.01 < 1.0) {
            QPointF currentPoint = path.pointAtPercent(t);
            QPointF nextPoint = path.pointAtPercent(t + 0.01);
            painter->drawLine(currentPoint, nextPoint);
        }
    }

    painter->restore();
}
