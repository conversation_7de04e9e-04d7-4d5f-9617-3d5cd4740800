#ifndef BRUSHTIPRENDERER_H
#define BRUSHTIPRENDERER_H

#include <QPainter>
#include <QPen>
#include <QBrush>
#include <QPainterPath>
#include <QPointF>
#include "../core/WhiteBoardTypes.h"

/**
 * @brief 笔锋渲染器 - 模拟毛笔笔锋效果
 *
 * 核心功能：
 * 1. 在笔画起始处绘制笔锋（尖锐到粗糙的过渡）
 * 2. 轻量级实现，不影响性能
 * 3. 只对自由绘制工具生效
 */
class BrushTipRenderer
{
public:
    /**
     * @brief 使用笔锋效果绘制路径
     * @param painter 绘制器
     * @param path 要绘制的路径
     * @param pen 画笔
     * @param brush 画刷
     * @param toolType 工具类型
     */
    static void drawPathWithBrushTip(QPainter* painter, 
                                   const QPainterPath& path,
                                   const QPen& pen, 
                                   const QBrush& brush,
                                   ToolType toolType);

private:
    /**
     * @brief 绘制笔锋起始效果
     * @param painter 绘制器
     * @param startPoint 起始点
     * @param secondPoint 第二个点（用于确定方向）
     * @param pen 画笔
     */
    static void drawBrushTip(QPainter* painter,
                           const QPointF& startPoint,
                           const QPointF& secondPoint,
                           const QPen& pen);

    /**
     * @brief 从路径中提取起始点和方向点
     * @param path 路径
     * @param startPoint 输出起始点
     * @param directionPoint 输出方向点
     * @return 是否成功提取
     */
    static bool extractStartPoints(const QPainterPath& path,
                                 QPointF& startPoint,
                                 QPointF& directionPoint);

    // 配置常量
    static constexpr bool BRUSH_TIP_ENABLED = true;     // 是否启用笔锋
    static constexpr qreal TIP_LENGTH_RATIO = 0.8;      // 笔锋长度比例（相对于笔宽）
    static constexpr int TIP_SEGMENTS = 5;              // 笔锋渐变段数
    static constexpr qreal TIP_ALPHA_START = 0.3;       // 笔锋起始透明度
};

#endif // BRUSHTIPRENDERER_H
