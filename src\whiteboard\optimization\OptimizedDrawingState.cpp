#include "OptimizedDrawingState.h"
#include "../tools/ShapeToolManager.h"
#include "../utils/DrawingPerformanceProfiler.h"
#include <QDateTime>

OptimizedDrawingState::OptimizedDrawingState()
{
}

void OptimizedDrawingState::setToolType(ToolType toolType)
{
    m_toolType = toolType;
    m_pathBuilder.setToolType(toolType);
}

void OptimizedDrawingState::setPen(const QPen& pen)
{
    m_pen = pen;
    m_pathBuilder.setPen(pen);  // 传递画笔信息给路径构建器
}

void OptimizedDrawingState::setBrush(const QBrush& brush)
{
    m_brush = brush;
}

void OptimizedDrawingState::startDrawing(const QPointF& startPoint)
{
    DRAWING_TIMER("OptimizedDrawingState::startDrawing");
    m_isDrawing = true;
    m_startPoint = startPoint;
    m_currentPoint = startPoint;
    m_lastPoint = startPoint;

    // 重置双缓冲区域
    m_lastDrawnRegion = QRectF();
    m_currentDrawRegion = QRectF();

    // 初始化速度笔锋
    if (m_velocityStrokeEnabled && isFreeDrawTool(m_toolType)) {
        m_currentVelocity = 0.0;
        m_lastVelocity = 0.0;
        m_currentStrokeWidth = m_pen.widthF();
        m_velocityTimer.start();
        m_lastTimestamp = 0;
    }

    // 启动路径构建
    m_pathBuilder.startPath(startPoint);

    // 添加初始脏区域
    if (m_dirtyRegionOptimizationEnabled) {
        qreal penWidth = qMax(m_pen.widthF(), 2.0);
        qreal radius = qMax(penWidth + 3.0, MIN_DIRTY_REGION_SIZE / 2.0);
        m_dirtyRegionManager.addDirtyPoint(startPoint, radius);
    }
}

void OptimizedDrawingState::continueDrawing(const QPointF& point)
{
    DRAWING_TIMER("OptimizedDrawingState::continueDrawing");
    if (!m_isDrawing) {
        return;
    }

    m_lastPoint = m_currentPoint;
    m_currentPoint = point;

    // 更新速度和笔触宽度
    if (m_velocityStrokeEnabled && isFreeDrawTool(m_toolType)) {
        updateVelocityAndStrokeWidth(point);
    }

    // 添加点到路径构建器
    m_pathBuilder.addPoint(point);

    // 处理工具特定的绘制逻辑（包括脏区域管理）
    handleToolSpecificDrawing(point);
}

void OptimizedDrawingState::finishDrawing()
{
    if (!m_isDrawing) {
        return;
    }

    m_isDrawing = false;



    m_pathBuilder.finishPath();

    // 清理双缓冲状态和脏区域
    if (m_dirtyRegionOptimizationEnabled) {
        m_dirtyRegionManager.clearDirtyRegions();
        m_lastDrawnRegion = QRectF();
        m_currentDrawRegion = QRectF();
    }
}

void OptimizedDrawingState::cancelDrawing()
{
    m_isDrawing = false;



    m_pathBuilder.cancelPath();
    m_dirtyRegionManager.clearDirtyRegions();
}

QPainterPath OptimizedDrawingState::getCurrentPath() const
{
    // 直接返回PathBuilder的当前路径
    return m_pathBuilder.getCurrentPath();
}

QRectF OptimizedDrawingState::getCurrentBounds() const
{
    return m_pathBuilder.getCurrentBounds();
}

bool OptimizedDrawingState::hasDirtyRegions() const
{
    if (!m_dirtyRegionOptimizationEnabled) {
        return m_pathBuilder.hasPath();
    }
    
    return m_dirtyRegionManager.hasDirtyRegions();
}

void OptimizedDrawingState::clearDirtyRegions()
{
    m_dirtyRegionManager.clearDirtyRegions();
    m_pathBuilder.clearCache();
}

QRectF OptimizedDrawingState::getDirtyRegion()
{
    if (!m_dirtyRegionOptimizationEnabled) {
        return m_pathBuilder.getCurrentBounds();
    }

    // 获取当前需要重绘的区域
    QRectF redrawRegion = m_dirtyRegionManager.getMergedDirtyRegion();

    m_lastDrawnRegion = m_currentDrawRegion;
    // 清除脏区域管理器，为下次更新做准备
    m_dirtyRegionManager.clearDirtyRegions();

    return redrawRegion;
}

bool OptimizedDrawingState::hasPath() const
{
    return m_pathBuilder.hasPath();
}

bool OptimizedDrawingState::needsUpdate() const
{
    return m_pathBuilder.needsRebuild() || hasDirtyRegions();
}

void OptimizedDrawingState::setBatchSize(int size)
{
    m_pathBuilder.setBatchSize(size);
}

void OptimizedDrawingState::handleToolSpecificDrawing(const QPointF& point)
{
    if (!m_dirtyRegionOptimizationEnabled) {
        return;
    }

    qreal penWidth = qMax(m_pen.widthF(), 1.0);
    qreal margin = penWidth + 5.0; // 增加边距确保完全覆盖

    handleShapeToolDrawing(point, margin);

}

void OptimizedDrawingState::handleShapeToolDrawing(const QPointF& point, qreal margin)
{
    // 计算当前绘制区域
    QRectF currentRegion = calculateShapeRegion(point, margin);

    // 清除脏区域管理器
    m_dirtyRegionManager.clearDirtyRegions();

    // 使用连接区域方法，确保快速移动时不会有间隙
    m_dirtyRegionManager.addConnectedRegions(m_lastDrawnRegion, currentRegion);

    // 更新记录
    m_currentDrawRegion = currentRegion;
}

QRectF OptimizedDrawingState::calculateShapeRegion(const QPointF& point, qreal margin)
{
    QRectF region;

    switch (m_toolType) {
    case ToolType::Line:
    case ToolType::DashedLine:
        // 直线：创建包含整条线的矩形，需要normalized确保正确的边界
        region = QRectF(m_startPoint, point).normalized();
        region = region.adjusted(-margin, -margin, margin, margin);
        break;

    case ToolType::Rectangle:
    case ToolType::Square:
    case ToolType::Ellipse:
    case ToolType::Circle:
    case ToolType::Triangle:
    case ToolType::RightTriangle:
        {
            // 形状工具：使用工具的getBoundingRect方法，它会正确处理约束
            ShapeToolManager* manager = ShapeToolManager::instance();
            if (manager && manager->hasToolType(m_toolType)) {
                AbstractShapeTool* tool = manager->getTool(m_toolType);
                if (tool) {
                    region = tool->getBoundingRect(m_startPoint, point);
                    // 确保区域是正向的，用于脏区域计算
                    region = region.normalized();
                    region = region.adjusted(-margin, -margin, margin, margin);
                    break;
                }
            }
            // 回退处理
            region = QRectF(m_startPoint, point).normalized();
            region = region.adjusted(-margin, -margin, margin, margin);
        }
        break;

    case ToolType::Arrow:
        {
            // 箭头：需要更大的边距
            region = QRectF(m_startPoint, point).normalized();
            qreal arrowMargin = margin + 30.0;
            region = region.adjusted(-arrowMargin, -arrowMargin, arrowMargin, arrowMargin);
            break;
        }
    case ToolType::FreeDraw:
        region = QRectF(m_lastPoint, point).normalized();
        region = region.adjusted(-margin, -margin, margin, margin);
        break;

    default:
        // 默认处理
        region = QRectF(m_startPoint, point).normalized();
        region = region.adjusted(-margin, -margin, margin, margin);
        break;
    }

    return ensureMinimumRegionSize(region);
}

QRectF OptimizedDrawingState::ensureMinimumRegionSize(const QRectF& region) const
{
    if (region.isEmpty()) {
        return region;
    }

    QRectF result = region;

    // 确保宽度至少为最小尺寸
    if (result.width() < MIN_DIRTY_REGION_SIZE) {
        qreal expansion = (MIN_DIRTY_REGION_SIZE - result.width()) / 2.0;
        result.setLeft(result.left() - expansion);
        result.setRight(result.right() + expansion);
    }

    // 确保高度至少为最小尺寸
    if (result.height() < MIN_DIRTY_REGION_SIZE) {
        qreal expansion = (MIN_DIRTY_REGION_SIZE - result.height()) / 2.0;
        result.setTop(result.top() - expansion);
        result.setBottom(result.bottom() + expansion);
    }

    return result;
}

// 速度笔锋实现方法
void OptimizedDrawingState::updateVelocityAndStrokeWidth(const QPointF& point)
{
    qreal velocity = calculateVelocity(point);

    // 应用低通滤波器平滑速度
    m_currentVelocity = VELOCITY_FILTER_WEIGHT * velocity +
                       (1.0 - VELOCITY_FILTER_WEIGHT) * m_lastVelocity;

    // 根据速度计算笔触宽度
    m_currentStrokeWidth = velocityToStrokeWidth(m_currentVelocity);

    m_lastVelocity = m_currentVelocity;
}

qreal OptimizedDrawingState::calculateVelocity(const QPointF& point)
{
    qint64 currentTime = m_velocityTimer.elapsed();

    if (m_lastTimestamp == 0) {
        m_lastTimestamp = currentTime;
        return 0.0;
    }

    qint64 deltaTime = currentTime - m_lastTimestamp;
    if (deltaTime <= 0) {
        return m_lastVelocity;
    }

    // 计算距离
    QPointF delta = point - m_lastPoint;
    qreal distance = qSqrt(delta.x() * delta.x() + delta.y() * delta.y());

    // 计算速度 (像素/毫秒)
    qreal velocity = distance / deltaTime;

    m_lastTimestamp = currentTime;
    return velocity;
}

qreal OptimizedDrawingState::velocityToStrokeWidth(qreal velocity) const
{
    qreal baseWidth = m_pen.widthF();
    qreal maxWidth = baseWidth * MAX_STROKE_WIDTH_MULTIPLIER;

    // 速度越快，线条越细（模拟快速书写）
    qreal normalizedVelocity = qBound(0.0, velocity / VELOCITY_THRESHOLD, 1.0);

    // 使用指数函数使变化更自然
    qreal widthFactor = 1.0 - qPow(normalizedVelocity, 0.6) * 0.7;

    qreal resultWidth = baseWidth + (maxWidth - baseWidth) * (1.0 - widthFactor);
    return qMax(MIN_STROKE_WIDTH, resultWidth);
}