#include "VelocityStrokeRenderer.h"
#include <QDebug>
#include <QtMath>

VelocityStrokeRenderer::VelocityStrokeRenderer()
{
}

void VelocityStrokeRenderer::drawVelocityStroke(QPainter* painter, 
                                              const QPainterPath& path,
                                              const QPen& pen, 
                                              const QBrush& brush,
                                              qreal currentVelocity,
                                              qreal currentWidth,
                                              ToolType toolType)
{
    if (path.isEmpty() || !painter) {
        return;
    }

    // 保存原始状态
    painter->save();
    
    // 检查是否启用速度笔锋
    if (!VELOCITY_STROKE_ENABLED || !isFreeDrawTool(toolType)) {
        // 回退到普通绘制
        painter->setPen(pen);
        painter->setBrush(brush);
        painter->drawPath(path);
        painter->restore();
        return;
    }

    // 提取速度点
    QVector<VelocityPoint> velocityPoints = extractVelocityPoints(path, currentWidth);
    
    if (velocityPoints.size() < 2) {
        // 点太少，使用普通绘制
        painter->setPen(pen);
        painter->setBrush(brush);
        painter->drawPath(path);
        painter->restore();
        return;
    }

    // 绘制变宽度笔触
    drawVariableWidthStroke(painter, velocityPoints, pen);
    
    painter->restore();
}

QVector<VelocityStrokeRenderer::VelocityPoint> VelocityStrokeRenderer::extractVelocityPoints(
    const QPainterPath& path, qreal baseWidth)
{
    QVector<VelocityPoint> points;
    
    if (path.isEmpty()) {
        return points;
    }

    // 简化实现：从路径中提取点，并估算速度
    for (int i = 0; i < path.elementCount(); ++i) {
        QPainterPath::Element element = path.elementAt(i);
        QPointF pos(element.x, element.y);
        
        qreal velocity = 0.0;
        qreal width = baseWidth;
        
        // 简单的速度估算（基于点间距离）
        if (i > 0) {
            QPainterPath::Element prevElement = path.elementAt(i - 1);
            QPointF prevPos(prevElement.x, prevElement.y);
            qreal distance = QLineF(prevPos, pos).length();
            velocity = distance * 0.1; // 简化的速度计算
            
            // 根据速度调整宽度
            qreal normalizedVel = qBound(0.0, velocity / 3.0, 1.0);
            width = baseWidth * (1.0 + (1.0 - normalizedVel) * 1.5);
        }
        
        points.append(VelocityPoint(pos, velocity, width));
    }
    
    return points;
}

void VelocityStrokeRenderer::drawVariableWidthStroke(QPainter* painter, 
                                                    const QVector<VelocityPoint>& points,
                                                    const QPen& pen)
{
    if (points.size() < 2) {
        return;
    }

    painter->setRenderHint(QPainter::Antialiasing, true);
    painter->setPen(Qt::NoPen);
    painter->setBrush(QBrush(pen.color()));

    // 使用圆形笔刷方法绘制平滑笔触
    for (int i = 0; i < points.size() - 1; ++i) {
        drawSmoothStroke(painter, points[i], points[i + 1], pen);
    }
}

void VelocityStrokeRenderer::drawSmoothStroke(QPainter* painter,
                                            const VelocityPoint& from,
                                            const VelocityPoint& to,
                                            const QPen& pen)
{
    QLineF line(from.position, to.position);
    qreal length = line.length();
    
    if (length < 1.0) {
        // 线段太短，绘制单个圆点
        painter->drawEllipse(from.position, from.width / 2, from.width / 2);
        return;
    }
    
    // 计算绘制步数
    int steps = qMax(MIN_STROKE_SEGMENTS, (int)(length / STROKE_SMOOTHNESS));
    
    for (int i = 0; i <= steps; ++i) {
        qreal t = (qreal)i / steps;
        
        // 插值位置
        QPointF pos = from.position + t * (to.position - from.position);
        
        // 插值宽度
        qreal width = from.width + t * (to.width - from.width);
        
        // 绘制圆形笔刷
        qreal radius = width / 2.0;
        painter->drawEllipse(pos, radius, radius);
    }
}

QPainterPath VelocityStrokeRenderer::createVariableWidthPath(const QVector<VelocityPoint>& points)
{
    QPainterPath path;
    
    if (points.size() < 2) {
        return path;
    }

    QPolygonF leftSide, rightSide;
    
    for (int i = 0; i < points.size() - 1; ++i) {
        const VelocityPoint& current = points[i];
        const VelocityPoint& next = points[i + 1];
        
        QPointF normal = calculateNormal(current.position, next.position);
        
        qreal halfWidth = current.width / 2.0;
        leftSide << current.position + normal * halfWidth;
        rightSide.prepend(current.position - normal * halfWidth);
    }
    
    // 处理最后一个点
    if (!points.isEmpty()) {
        const VelocityPoint& last = points.last();
        QPointF normal = calculateNormal(points[points.size()-2].position, last.position);
        qreal halfWidth = last.width / 2.0;
        leftSide << last.position + normal * halfWidth;
        rightSide.prepend(last.position - normal * halfWidth);
    }
    
    // 创建闭合路径
    path.addPolygon(leftSide + rightSide);
    
    return path;
}

QPointF VelocityStrokeRenderer::calculateNormal(const QPointF& from, const QPointF& to)
{
    QPointF direction = to - from;
    qreal length = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());
    
    if (length < 1e-6) {
        return QPointF(0, 1); // 默认法向量
    }
    
    // 计算单位法向量（逆时针旋转90度）
    QPointF normal(-direction.y() / length, direction.x() / length);
    return normal;
}
