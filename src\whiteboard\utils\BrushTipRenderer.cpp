#include "BrushTipRenderer.h"
#include <QDebug>
#include <QtMath>

void BrushTipRenderer::drawPathWithBrushTip(QPainter* painter,
                                           const QPainterPath& path,
                                           const QPen& pen,
                                           const QBrush& brush,
                                           ToolType toolType)
{
    if (path.isEmpty() || !painter) {
        return;
    }

    // 保存原始状态
    painter->save();
    
    // 先绘制正常路径
    painter->setPen(pen);
    painter->setBrush(brush);
    painter->drawPath(path);
    
    // 只对自由绘制工具添加笔锋效果
    if (BRUSH_TIP_ENABLED && isFreeDrawTool(toolType)) {
        QPointF startPoint, directionPoint;
        if (extractStartPoints(path, startPoint, directionPoint)) {
            drawBrushTip(painter, startPoint, directionPoint, pen);
        }
    }
    
    painter->restore();
}

void BrushTipRenderer::drawBrushTip(QPainter* painter,
                                    const QPointF& startPoint,
                                    const QPointF& secondPoint,
                                    const QPen& pen)
{
    qreal penWidth = pen.widthF();
    if (penWidth < 1.0) {
        return; // 笔太细，不绘制笔锋
    }
    
    // 计算笔锋方向（从第二个点指向起始点）
    QPointF direction = startPoint - secondPoint;
    qreal length = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());
    
    if (length < 1.0) {
        return; // 距离太短，无法确定方向
    }
    
    // 标准化方向向量
    direction = direction / length;
    
    // 计算笔锋长度
    qreal tipLength = penWidth * TIP_LENGTH_RATIO;
    
    // 计算垂直方向（用于笔锋宽度）
    QPointF perpendicular(-direction.y(), direction.x());
    
    painter->setRenderHint(QPainter::Antialiasing, true);
    painter->setPen(Qt::NoPen);
    
    QColor tipColor = pen.color();
    
    // 绘制渐变的笔锋段
    for (int i = 0; i < TIP_SEGMENTS; ++i) {
        qreal t = (qreal)i / TIP_SEGMENTS;
        qreal nextT = (qreal)(i + 1) / TIP_SEGMENTS;
        
        // 计算当前段的位置
        QPointF currentPos = startPoint + direction * tipLength * t;
        QPointF nextPos = startPoint + direction * tipLength * nextT;
        
        // 计算当前段的宽度（从细到粗）
        qreal currentWidth = penWidth * t * 0.3; // 起始很细
        qreal nextWidth = penWidth * nextT * 0.8; // 逐渐变粗
        
        // 计算透明度（从透明到不透明）
        qreal alpha = TIP_ALPHA_START + (1.0 - TIP_ALPHA_START) * t;
        tipColor.setAlphaF(alpha);
        painter->setBrush(QBrush(tipColor));
        
        // 绘制梯形段
        QPolygonF segment;
        segment << currentPos + perpendicular * currentWidth / 2
                << currentPos - perpendicular * currentWidth / 2
                << nextPos - perpendicular * nextWidth / 2
                << nextPos + perpendicular * nextWidth / 2;
        
        painter->drawPolygon(segment);
    }
}

bool BrushTipRenderer::extractStartPoints(const QPainterPath& path,
                                         QPointF& startPoint,
                                         QPointF& directionPoint)
{
    if (path.elementCount() < 2) {
        return false;
    }
    
    // 获取起始点
    QPainterPath::Element firstElement = path.elementAt(0);
    startPoint = QPointF(firstElement.x, firstElement.y);
    
    // 查找第二个有效点（距离起始点足够远）
    const qreal minDistance = 3.0; // 最小距离阈值
    
    for (int i = 1; i < path.elementCount(); ++i) {
        QPainterPath::Element element = path.elementAt(i);
        QPointF point(element.x, element.y);
        
        qreal distance = qSqrt(qPow(point.x() - startPoint.x(), 2) + 
                              qPow(point.y() - startPoint.y(), 2));
        
        if (distance >= minDistance) {
            directionPoint = point;
            return true;
        }
    }
    
    return false;
}
