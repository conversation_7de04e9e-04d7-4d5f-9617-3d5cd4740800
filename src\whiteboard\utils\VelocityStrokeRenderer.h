#ifndef VELOCITYSTROKERENDERER_H
#define VELOCITYSTROKERENDERER_H

#include <QPainter>
#include <QPen>
#include <QBrush>
#include <QPainterPath>
#include <QPointF>
#include <QVector>
#include "../core/WhiteBoardTypes.h"

/**
 * @brief 速度笔锋渲染器 - 基于速度的变宽度笔触渲染
 * 
 * 核心功能：
 * 1. 根据绘制速度动态调整笔触宽度
 * 2. 平滑的宽度过渡效果
 * 3. 与现有FeatheringRenderer兼容
 * 4. 高性能的实时渲染
 */
class VelocityStrokeRenderer
{
public:
    /**
     * @brief 速度点结构 - 包含位置、速度和宽度信息
     */
    struct VelocityPoint {
        QPointF position;
        qreal velocity;
        qreal width;
        
        VelocityPoint() : velocity(0.0), width(2.0) {}
        VelocityPoint(const QPointF& pos, qreal vel, qreal w) 
            : position(pos), velocity(vel), width(w) {}
    };

    VelocityStrokeRenderer();
    ~VelocityStrokeRenderer() = default;

    /**
     * @brief 使用速度笔锋绘制路径
     * @param painter 绘制器
     * @param path 基础路径
     * @param pen 基础画笔
     * @param brush 画刷
     * @param currentVelocity 当前速度
     * @param currentWidth 当前宽度
     * @param toolType 工具类型
     */
    static void drawVelocityStroke(QPainter* painter, 
                                 const QPainterPath& path,
                                 const QPen& pen, 
                                 const QBrush& brush,
                                 qreal currentVelocity,
                                 qreal currentWidth,
                                 ToolType toolType);

    /**
     * @brief 从路径提取速度点序列
     * @param path 路径
     * @param baseWidth 基础宽度
     * @return 速度点序列
     */
    static QVector<VelocityPoint> extractVelocityPoints(const QPainterPath& path, qreal baseWidth);

private:
    /**
     * @brief 绘制变宽度线段
     * @param painter 绘制器
     * @param points 速度点序列
     * @param pen 画笔
     */
    static void drawVariableWidthStroke(QPainter* painter, 
                                      const QVector<VelocityPoint>& points,
                                      const QPen& pen);

    /**
     * @brief 使用圆形笔刷绘制平滑笔触
     * @param painter 绘制器
     * @param from 起始点
     * @param to 结束点
     * @param pen 画笔
     */
    static void drawSmoothStroke(QPainter* painter,
                               const VelocityPoint& from,
                               const VelocityPoint& to,
                               const QPen& pen);

    /**
     * @brief 创建变宽度多边形路径
     * @param points 速度点序列
     * @return 多边形路径
     */
    static QPainterPath createVariableWidthPath(const QVector<VelocityPoint>& points);

    /**
     * @brief 计算线段的法向量
     * @param from 起始点
     * @param to 结束点
     * @return 单位法向量
     */
    static QPointF calculateNormal(const QPointF& from, const QPointF& to);

    // 配置常量
    static constexpr qreal STROKE_SMOOTHNESS = 2.0;  // 笔触平滑度
    static constexpr int MIN_STROKE_SEGMENTS = 3;    // 最小笔触段数
    static constexpr bool VELOCITY_STROKE_ENABLED = true;  // 是否启用速度笔锋
};

#endif // VELOCITYSTROKERENDERER_H
